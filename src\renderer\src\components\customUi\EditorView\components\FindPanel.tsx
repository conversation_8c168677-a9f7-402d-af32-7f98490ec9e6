import React, { useState, useEffect, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { BlockNoteEditor } from '@blocknote/core';
import { Input } from '@/ui/input';
import { Button } from '@/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/ui/tooltip';
import {
  Search,
  ChevronUp,
  ChevronDown,
  X,
  Replace,
  List,
  CaseSensitive
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  findInDocument,
  navigateToMatch,
  createFindResult,
  getNextMatchIndex,
  getPreviousMatchIndex,
  replaceInDocument,
  replaceAllInDocument,
  highlightAllMatches,
  clearAllHighlights,
  cleanupInvalidHighlights,
  type FindResult
} from '../utils/findUtils';

interface FindPanelProps {
  isOpen: boolean;
  onClose: () => void;
  editor: BlockNoteEditor | null;
}

export const FindPanel: React.FC<FindPanelProps> = ({
  isOpen,
  onClose,
  editor
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [replaceTerm, setReplaceTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [showReplace, setShowReplace] = useState(false);
  const [showResults, setShowResults] = useState(true); // Default to open
  const [findResult, setFindResult] = useState<FindResult>({
    matches: [],
    currentMatchIndex: -1,
    totalMatches: 0
  });

  const searchInputRef = useRef<HTMLInputElement>(null);
  const replaceInputRef = useRef<HTMLInputElement>(null);

  // Perform search when search term or case sensitivity changes
  const performSearch = useCallback(() => {
    if (!editor || !searchTerm.trim()) {
      clearAllHighlights(editor);
      setFindResult({
        matches: [],
        currentMatchIndex: -1,
        totalMatches: 0
      });
      return;
    }

    const matches = findInDocument(editor.document, searchTerm, caseSensitive);
    const result = createFindResult(matches, 0);
    setFindResult(result);

    // Highlight all matches with first one as current
    highlightAllMatches(editor, result.matches, 0);

    // Navigate to first match if any
    if (result.matches.length > 0) {
      navigateToMatch(editor, result.matches[0]);
    }
  }, [editor, searchTerm, caseSensitive]);

  // Navigate to next match
  const handleNext = useCallback(() => {
    if (findResult.totalMatches === 0 || !editor) return;

    const nextIndex = getNextMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    const updatedResult = { ...findResult, currentMatchIndex: nextIndex };
    setFindResult(updatedResult);

    // Update highlighting to show new current match
    highlightAllMatches(editor, updatedResult.matches, nextIndex);

    if (updatedResult.matches[nextIndex]) {
      navigateToMatch(editor, updatedResult.matches[nextIndex]);
    }
  }, [findResult, editor]);

  // Navigate to previous match
  const handlePrevious = useCallback(() => {
    if (findResult.totalMatches === 0 || !editor) return;

    const prevIndex = getPreviousMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    const updatedResult = { ...findResult, currentMatchIndex: prevIndex };
    setFindResult(updatedResult);

    // Update highlighting to show new current match
    highlightAllMatches(editor, updatedResult.matches, prevIndex);

    if (updatedResult.matches[prevIndex]) {
      navigateToMatch(editor, updatedResult.matches[prevIndex]);
    }
  }, [findResult, editor]);

  // Replace current match
  const handleReplace = useCallback(() => {
    if (!editor || findResult.totalMatches === 0 || !replaceTerm) return;

    const currentMatch = findResult.matches[findResult.currentMatchIndex];
    if (!currentMatch) return;

    replaceInDocument(editor, currentMatch, replaceTerm);

    // Refresh search results after replace
    setTimeout(() => performSearch(), 100);
  }, [editor, findResult, replaceTerm, performSearch]);

  // Replace all matches
  const handleReplaceAll = useCallback(() => {
    if (!editor || findResult.totalMatches === 0 || !replaceTerm) return;

    replaceAllInDocument(editor, findResult.matches, replaceTerm);

    // Refresh search results after replace all
    setTimeout(() => performSearch(), 100);
  }, [editor, findResult, replaceTerm, performSearch]);

  // Navigate to specific match from results list
  const handleMatchClick = useCallback((matchIndex: number) => {
    if (!editor || !findResult.matches[matchIndex]) return;

    const updatedResult = { ...findResult, currentMatchIndex: matchIndex };
    setFindResult(updatedResult);

    // Update highlighting to show new current match
    highlightAllMatches(editor, updatedResult.matches, matchIndex);

    navigateToMatch(editor, findResult.matches[matchIndex]);
  }, [editor, findResult]);

  // Handle keyboard shortcuts
  const handleSearchKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (e.shiftKey) {
        handlePrevious();
      } else {
        handleNext();
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  }, [handleNext, handlePrevious, onClose]);

  const handleReplaceKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleReplace();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  }, [handleReplace, onClose]);

  // Focus input when panel opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
        searchInputRef.current?.select();
      }, 100);
    }
  }, [isOpen]);

  // Perform search when dependencies change
  useEffect(() => {
    performSearch();
  }, [performSearch]);

  // Reset search when panel closes
  useEffect(() => {
    if (!isOpen && editor) {
      clearAllHighlights(editor);
      setSearchTerm('');
      setReplaceTerm('');
      setShowReplace(false);
      setShowResults(true); // Reset to default open state
      setFindResult({
        matches: [],
        currentMatchIndex: -1,
        totalMatches: 0
      });
    }
  }, [isOpen, editor]);

  // Add effect to cleanup invalid highlights when text changes - optimized for performance
  useEffect(() => {
    if (!editor || !searchTerm.trim()) return;

    let timeoutId: NodeJS.Timeout;

    const cleanup = () => {
      cleanupInvalidHighlights(editor, searchTerm, caseSensitive);
    };

    // Debounced cleanup to avoid excessive DOM queries
    const debouncedCleanup = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(cleanup, 500);
    };

    // Listen for editor changes to trigger cleanup
    const unsubscribe = editor.onChange(debouncedCleanup);

    return () => {
      clearTimeout(timeoutId);
      unsubscribe();
    };
  }, [editor, searchTerm, caseSensitive]);

  const displayText = findResult.totalMatches > 0
    ? `${findResult.currentMatchIndex + 1} of ${findResult.totalMatches}`
    : findResult.totalMatches === 0 && searchTerm.trim()
      ? 'No matches'
      : '';

  if (!isOpen) return null;

  const panelContent = (
    <TooltipProvider delayDuration={100}>
      <div className="fixed top-40 right-4 z-[1000] bg-background border border-border rounded-lg shadow-lg min-w-80 max-w-96" style={{ position: 'fixed' }}>
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-border">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Find & Replace</span>
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Close (Esc)</p>
            </TooltipContent>
          </Tooltip>
        </div>

      <div className="p-3 space-y-3">
        {/* Search Input Row */}
        <div className="flex items-center gap-2">
          <div className="relative flex-1">
            <Input
              ref={searchInputRef}
              type="text"
              placeholder="Find..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleSearchKeyDown}
              className="h-8 text-sm pr-20"
            />
            <div className="absolute right-1 top-1/2 -translate-y-1/2 flex items-center gap-1">
              <span className="text-xs text-muted-foreground min-w-12 text-center">
                {displayText}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={findResult.totalMatches === 0}
                  className="h-8 w-8 p-0"
                >
                  <ChevronUp className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Previous (Shift+Enter)</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleNext}
                  disabled={findResult.totalMatches === 0}
                  className="h-8 w-8 p-0"
                >
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Next (Enter)</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Replace Input Row (conditionally shown) */}
        {showReplace && (
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Input
                ref={replaceInputRef}
                type="text"
                placeholder="Replace..."
                value={replaceTerm}
                onChange={(e) => setReplaceTerm(e.target.value)}
                onKeyDown={handleReplaceKeyDown}
                className="h-8 text-sm"
              />
            </div>
            <div className="flex items-center gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleReplace}
                    disabled={findResult.totalMatches === 0 || !replaceTerm}
                    className="h-8 px-2 text-xs"
                  >
                    Replace
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Replace current match</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleReplaceAll}
                    disabled={findResult.totalMatches === 0 || !replaceTerm}
                    className="h-8 px-2 text-xs"
                  >
                    All
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Replace all matches</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
        )}

        {/* Controls Row */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCaseSensitive(!caseSensitive)}
                  className={cn(
                    "h-8 w-8 p-0",
                    caseSensitive && "bg-accent text-accent-foreground"
                  )}
                >
                  <CaseSensitive className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Match case</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowReplace(!showReplace)}
                  className={cn(
                    "h-8 w-8 p-0",
                    showReplace && "bg-accent text-accent-foreground"
                  )}
                >
                  <Replace className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Toggle replace</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowResults(!showResults)}
                  disabled={findResult.totalMatches === 0}
                  className={cn(
                    "h-8 w-8 p-0",
                    showResults && "bg-accent text-accent-foreground"
                  )}
                >
                  <List className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Show all results</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Results List (conditionally shown) */}
        {showResults && findResult.totalMatches > 0 && (
          <div className="border-t border-border pt-3">
            <div className="text-xs text-muted-foreground mb-2">
              {findResult.totalMatches} result{findResult.totalMatches !== 1 ? 's' : ''}
            </div>
            <div className="max-h-60 overflow-y-auto space-y-1 pr-1">
              {findResult.matches.map((match, index) => (
                <button
                  key={`${match.blockId}-${match.textIndex}`}
                  onClick={() => handleMatchClick(index)}
                  className={cn(
                    "w-full text-left p-2 rounded text-xs hover:bg-accent hover:text-accent-foreground transition-colors",
                    index === findResult.currentMatchIndex && "bg-accent text-accent-foreground"
                  )}
                >
                  <div className="truncate">
                    {match.blockText.substring(0, match.textIndex)}
                    <span className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
                      {match.text}
                    </span>
                    {match.blockText.substring(match.textIndex + match.length)}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
      </div>
    </TooltipProvider>
  );

  // Use portal to render at document root for proper fixed positioning
  return createPortal(panelContent, document.body);
};
